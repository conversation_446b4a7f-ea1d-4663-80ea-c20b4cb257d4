from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from datetime import datetime

@dataclass
class TradeSignal:
    """Class for representing a trade signal."""
    symbol: str
    direction: str  # "BUY" or "SELL"
    entry_price: float
    stop_loss: float
    entry_price_high: Optional[float] = None  # For entry range
    targets: List[float] = field(default_factory=list)
    timestamp: float = field(default_factory=lambda: datetime.now().timestamp())
    market_type: str = "SPOT"  # "SPOT" or "FUTURES"
    leverage: Optional[int] = None  # Only for futures
    concepts_met: List[str] = field(default_factory=list)
    win_rate: float = 0.0
    explanation: str = ""
    status: str = "pending"  # pending, entry_triggered, target_hit, stop_loss_hit, closed
    targets_hit: List[int] = field(default_factory=list)  # Indices of targets that have been hit

    def to_dict(self) -> Dict[str, Any]:
        """Convert the trade signal to a dictionary."""
        return {
            "symbol": self.symbol,
            "direction": self.direction,
            "entry_price": self.entry_price,
            "entry_price_high": self.entry_price_high,
            "stop_loss": self.stop_loss,
            "targets": self.targets,
            "timestamp": self.timestamp,
            "market_type": self.market_type,
            "leverage": self.leverage,
            "concepts_met": self.concepts_met,
            "win_rate": self.win_rate,
            "explanation": self.explanation,
            "status": self.status,
            "targets_hit": self.targets_hit
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TradeSignal':
        """Create a trade signal from a dictionary."""
        return cls(
            symbol=data.get("symbol", ""),
            direction=data.get("direction", ""),
            entry_price=data.get("entry_price", 0.0),
            stop_loss=data.get("stop_loss", 0.0),
            entry_price_high=data.get("entry_price_high"),
            targets=data.get("targets", []),
            timestamp=data.get("timestamp", datetime.now().timestamp()),
            market_type=data.get("market_type", "SPOT"),
            leverage=data.get("leverage"),
            concepts_met=data.get("concepts_met", []),
            win_rate=data.get("win_rate", 0.0),
            explanation=data.get("explanation", ""),
            status=data.get("status", "pending"),
            targets_hit=data.get("targets_hit", [])
        )

    def format_message(self) -> str:
        """Format the trade signal as a message for Telegram."""
        entry_range = f"{self.entry_price}"
        if self.entry_price_high:
            entry_range = f"{self.entry_price} - {self.entry_price_high}"

        # Format direction based on market type
        formatted_direction = self._format_direction_for_market()

        # Format targets with status indicators
        targets_lines = []
        for i, target in enumerate(self.targets):
            target_num = i + 1
            if i in self.targets_hit:
                # Target has been hit
                targets_lines.append(f"Target {target_num}: {target} ✅")
            else:
                targets_lines.append(f"Target {target_num}: {target}")

        targets_str = "\n".join(targets_lines)

        concepts_str = ", ".join(self.concepts_met)

        leverage_str = f"\nRecommended Leverage: {self.leverage}x" if self.leverage else ""

        # Status indicators
        status_emoji = "🔔"  # Default for pending
        status_text = ""

        if self.status == "entry_triggered":
            status_emoji = "✅"
            status_text = "\n\n⚠️ <b>ENTRY TRIGGERED!</b> ⚠️"
        elif self.status == "target_hit":
            status_emoji = "🎯"
            hit_targets = [str(t+1) for t in self.targets_hit]
            status_text = f"\n\n🎯 <b>TARGET(S) {', '.join(hit_targets)} HIT!</b> 🎯"
        elif self.status == "stop_loss_hit":
            status_emoji = "❌"
            status_text = "\n\n❌ <b>STOP LOSS HIT!</b> ❌"
        elif self.status == "closed":
            status_emoji = "🏁"
            status_text = "\n\n🏁 <b>TRADE CLOSED</b> 🏁"

        # Main title changes based on status
        if self.status == "pending":
            title = f"🚨 <b>NEW {self.market_type} SIGNAL</b> 🚨"
        else:
            title = f"{status_emoji} <b>{self.market_type} SIGNAL UPDATE</b> {status_emoji}"

        message = (
            f"{title}\n\n"
            f"<b>Pair:</b> {self.symbol}\n"
            f"<b>Direction:</b> {formatted_direction}\n"
            f"<b>Entry:</b> {entry_range}\n"
            f"<b>Stop Loss:</b> {self.stop_loss}\n\n"
            f"<b>Targets:</b>\n{targets_str}\n\n"
            f"<b>Win Rate:</b> {self.win_rate:.1f}%\n"
            f"<b>Concepts Met:</b> {concepts_str}{leverage_str}\n\n"
            f"<b>Analysis:</b>\n{self.explanation}{status_text}"
        )

        return message

    def _format_direction_for_market(self) -> str:
        """Format trade direction based on market type."""
        if self.market_type.upper() == "FUTURES":
            # For futures, use Long/Short terminology
            if self.direction.upper() == "BUY":
                return "Long"
            elif self.direction.upper() == "SELL":
                return "Short"
        # For spot trades, keep Buy/Sell terminology
        return self.direction.title()  # Buy or Sell

    def update_status(self, new_status: str, target_index: int = None) -> None:
        """
        Update the status of the trade signal.

        Args:
            new_status: The new status (entry_triggered, target_hit, stop_loss_hit, closed)
            target_index: The index of the target that was hit (if applicable)
        """
        self.status = new_status

        if new_status == "target_hit" and target_index is not None:
            if target_index not in self.targets_hit:
                self.targets_hit.append(target_index)
                # Sort targets_hit to ensure they're in order
                self.targets_hit.sort()

    def get_alert_message(self) -> str:
        """Get an alert message based on the current status."""
        if self.status == "entry_triggered":
            return self._get_entry_alert_message()
        elif self.status == "target_hit":
            return self._get_target_hit_alert_message()
        elif self.status == "stop_loss_hit":
            return self._get_stop_loss_alert_message()
        elif self.status == "closed":
            return self._get_trade_closed_message()
        return ""

    def _get_entry_alert_message(self) -> str:
        """Get the entry alert message."""
        formatted_direction = self._format_direction_for_market()
        return (
            f"⚠️ <b>ENTRY ALERT</b> ⚠️\n\n"
            f"<b>Pair:</b> {self.symbol}\n"
            f"<b>Direction:</b> {formatted_direction}\n"
            f"<b>Entry Price:</b> {self.entry_price}\n\n"
            f"Your trade entry has been triggered. Monitor the trade closely and follow your trading plan."
        )

    def _get_target_hit_alert_message(self) -> str:
        """Get the target hit alert message."""
        last_hit = self.targets_hit[-1] if self.targets_hit else 0
        target_price = self.targets[last_hit] if last_hit < len(self.targets) else 0

        return (
            f"🎯 <b>TARGET HIT ALERT</b> 🎯\n\n"
            f"<b>Pair:</b> {self.symbol}\n"
            f"<b>Direction:</b> {self.direction}\n"
            f"<b>Target {last_hit+1} Hit:</b> {target_price}\n\n"
            f"Congratulations! Target {last_hit+1} has been reached. "
            f"Consider taking profits or adjusting your stop loss."
        )

    def _get_stop_loss_alert_message(self) -> str:
        """Get the stop loss alert message."""
        return (
            f"❌ <b>STOP LOSS ALERT</b> ❌\n\n"
            f"<b>Pair:</b> {self.symbol}\n"
            f"<b>Direction:</b> {self.direction}\n"
            f"<b>Stop Loss:</b> {self.stop_loss}\n\n"
            f"Your stop loss has been triggered. The trade is now closed."
        )

    def _get_trade_closed_message(self) -> str:
        """Get the trade closed message."""
        return (
            f"🏁 <b>TRADE CLOSED</b> 🏁\n\n"
            f"<b>Pair:</b> {self.symbol}\n"
            f"<b>Direction:</b> {self.direction}\n\n"
            f"This trade has been closed. Thank you for trading with us!"
        )
